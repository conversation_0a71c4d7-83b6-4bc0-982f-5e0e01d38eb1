# Description

修改 .tekton 目录下的文件，删除原来 pipelineRun 中的 pipelineSpec，改为 pipelineRef, pipelineRef 的内容应该为：

```
pipelineRef:
    resolver: git
    params:
      - name: url
        value: "https://github.com/stolostron/konflux-build-catalog.git"
      - name: revision
        value: main
      - name: pathInRepo
        value: pipelines/common.yaml
```

除了添加pipelineRef以外, 不要做任何其他多余的改动

# Records

Single Repo Multi Branches:

| repo          | branch        | Pass | Result | PR  |
| ------------- | ------------- | ---- | ------ | --- |
| cluster-proxy | backplane-2.8 |      |        |     |
| cluster-proxy | backplane-2.7 |      |        |     |
| cluster-proxy | backplane-2.6 |      |        |     |
