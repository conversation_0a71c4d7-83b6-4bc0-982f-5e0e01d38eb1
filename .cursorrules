# Code Agent Workflow Rules

## Task Processing Rules

1. **Task Initialization**: Read `task-current.md` to get task description and project records table
2. **Process Each Project**: For each row in the records table:
   - Navigate to `workspace/<record.repo>`
   - Stash changes and fetch: `git stash && git fetch --all`
   - Checkout branch: `git checkout upstream/<record.branch>`
   - Create feature branch: `git checkout -b <short-task-description>`
   - Implement changes per task description
   - Commit with sign-off: `git commit -s -m "message"`
   - Create PR to upstream using `gh pr create`
   - Update record with pass/fail status, result summary, and PR link

## Git Configuration

- Remotes: `origin` (user fork), `upstream` (target for PRs)
- Default branch: `main`
- Release branches: `backplane-2.x` pattern

## Requirements

- All commits must be signed (`-s` flag)
- All text (commits, PRs, comments) must be in English
- Code comments must never use non-English characters
- PR must target upstream repo using: `gh pr create --repo stolostron/<repo> --base <branch> --head <user>:<branch>`
- Use `$'...'` syntax for PR body to handle escape sequences

## Command Reference

- List branches: `git --no-pager branch`
- Get username: `git config --get user.name`

## Error Handling

- Mark failed projects as `pass: no` with explanation
- Continue processing remaining projects after failures
